import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Alert,
  Chip
} from '@mui/material';
import {
  LocationCity as LocationCityIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import RBACUserManagement from '../../components/UserManagement/RBACUserManagement';
import { usePermissions } from '../../hooks/usePermissions';
import { hasPermission } from '../../utils/multiTenantRBAC';

const SubcityUserManagementRBAC = () => {
  const { user, dynamicRole } = usePermissions();

  // Check if user has permission to manage kebele users
  const canManageKebeleUsers = hasPermission(user, 'create_kebele_users');

  if (!user) {
    return <div>Loading...</div>;
  }

  if (!canManageKebeleUsers) {
    return (
      <Box sx={{ p: 3 }}>
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <SecurityIcon sx={{ fontSize: 64, color: 'error.main', mb: 2 }} />
          <Typography variant="h5" gutterBottom>
            Access Denied
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            You don't have permission to manage kebele users.
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Required permission: <Chip label="create_kebele_users" size="small" />
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Your current role: <Chip label={dynamicRole} size="small" color="primary" />
          </Typography>
        </Paper>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <LocationCityIcon />
          Subcity User Management
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage users for kebeles under your subcity administration
        </Typography>
      </Box>

      {/* Permission Info */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>Subcity Admin Permissions:</strong> You can create and manage users for kebeles that belong to your subcity.
          Users created will have access to their respective kebele tenants.
        </Typography>
      </Alert>

      {/* User Management Component */}
      <RBACUserManagement 
        tenantType="kebele"
        currentUser={user}
      />

      {/* Help Section */}
      <Paper sx={{ p: 3, mt: 3, bgcolor: 'grey.50' }}>
        <Typography variant="h6" gutterBottom>
          Available Roles for Kebele Users
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
          <Chip 
            label="Kebele Leader" 
            color="primary" 
            variant="outlined"
            size="small"
          />
          <Chip 
            label="Registration Clerk" 
            color="secondary" 
            variant="outlined"
            size="small"
          />
          <Chip 
            label="Print Operator" 
            color="warning" 
            variant="outlined"
            size="small"
          />
        </Box>
        <Typography variant="body2" color="text.secondary">
          • <strong>Kebele Leader:</strong> Can approve ID cards and verify documents locally<br/>
          • <strong>Registration Clerk:</strong> Can register citizens and generate ID cards<br/>
          • <strong>Print Operator:</strong> Can print ID cards and manage print queue
        </Typography>
      </Paper>

      {/* Workflow Information */}
      <Paper sx={{ p: 3, mt: 2, bgcolor: 'info.light', color: 'info.contrastText' }}>
        <Typography variant="h6" gutterBottom>
          Workflow Management
        </Typography>
        <Typography variant="body2">
          As a subcity admin, you can also manage workflow types for your kebeles:
        </Typography>
        <Box sx={{ mt: 1 }}>
          <Typography variant="body2">
            • <strong>Centralized Workflow:</strong> ID cards require subcity approval
          </Typography>
          <Typography variant="body2">
            • <strong>Autonomous Workflow:</strong> Kebeles can approve and print locally
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
};

export default SubcityUserManagementRBAC;
